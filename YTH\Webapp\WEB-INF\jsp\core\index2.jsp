<%@ page language="java" import="java.util.*" pageEncoding="utf-8"%>
<%@ page language="java" import="com.ccidit.core.cas.CCIDCAS" %>
<%@ page language="java" import="org.apache.struts2.ServletActionContext"%>
<%@ taglib prefix="nb" uri="/WEB-INF/tld/balancesystem.tld"%>
<%@ taglib prefix="s" uri="/struts-tags"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	response.setHeader("P3P","CP=\"IDC DSP COR ADM DEVi TAIi PSA PSD IVAi IVDi CONi HIS OUR IND CNT\""); 
	
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>商务部投资促进事务局科学化管理综合信息平台</title>

<script src="<%=request.getContextPath()%>/js/jquery/jquery-1.7.2.js"
	type="text/javascript" language="javascript"></script>
<script src="js/jquery/jquery.messager.js" type="text/javascript"
	language="javascript"></script>
<link href="css/global.css" rel="stylesheet" type="text/css" />
<link href="css/content.css" rel="stylesheet" type="text/css" />
<link
	href="<%=request.getContextPath()%>/js/jquery/fancybox/jquery.fancybox-1.3.4.css"
	rel="stylesheet" type="text/css" />

<script src="js/pub.js" type="text/javascript" language="javascript"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxcommon.js"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxtree.js"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxmenu.js"></script>
<script src="<%=request.getContextPath()%>/js/cvi_busy_lib.js"></script>
<script
	src="<%=request.getContextPath()%>/js/jquery/fancybox/jquery.mousewheel-3.0.4.pack.js"></script>
<script
	src="<%=request.getContextPath()%>/js/jquery/fancybox/jquery.fancybox-1.3.4.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/updatepopup2.js"></script>
<style type="text/css">
.selectedTreeRow_lor,.selectedTreeRow {
	font-weight: bold;
}
</style>
<script type="text/javascript">
//左侧面板隐藏
$(document).ready(function(){
  $(".red_middle").click(function(){
  $(".red_left").toggle();
  $(".red_right").toggleClass("trr");
 $(".red_middle").toggleClass("trm");
  });
});
</script>
<link href="css/hr/jquery.qtip.min.css" rel="stylesheet" />
<script src="js/hr/jquery.qtip.min.js"></script>
<script type="text/javascript">
    var pop;
     
    function turnWorkPlatform(){
    	<% String tmp = CCIDCAS.readToken(ServletActionContext.getRequest());%>
    	var url = "http://************:7001/cipa?token=" +'<%=tmp%>';
    	window.location.href = url;
    }
    
	function ShowIframe(params) //显示iframe
	{
		if(params=='1'){
			 pop=new Popup({ 
			    	contentType:1,
			    	isReloadOnClose:false,
			    	width:500,
			    	height:300,
			    	scrollType:"no",
			    	isSupportDraging:true

			    });
			    pop.setContent("contentUrl","userMgt_updateDefual.action");
			    pop.setContent("title","为了您的账户信息安全，请您修改系统默认密码");
			    pop.build();
			    pop.show();
			}
	      
	}
	function closeIframe(){
		pop.close();
	}

    $(function() {
      $("#tip span[title]").qtip();
    });

    var message="";
    function showMessage(){
               $.ajaxSetup ({
                  cache: false //关闭AJAX相应的缓存
               });
               $.getJSON("<%=request.getContextPath()%>/sysMess_getMessage.action",function(result){
                 if(result!=0){
                      $.messager.lays(250, 150);  
                      $.messager.anim('fade', 1000);
                      var message="";
                      
                      for(var i=0;i<result.length;i++){
                          var delNum1 = "a_11_"+result[i].messageId;
                          var delNum2 = "a_22_"+result[i].messageId;
                          var category = result[i].category;
                          var messageContent = result[i].messageContent;
                          if(category == null){
                              category = "";
                          }
                          if(messageContent == null){
                              continue;
                          }else{
                              if(category!=null & (category.length +  messageContent.length) > 18){     
                                 // messageContent = "<span id='#tip'><span title='"+ messageContent +"'>"+messageContent.substring(0,18-category.length)+'..'+"</span></span>";
                            	  messageContent = "<span id='#tip'><span title='"+ messageContent +"'>"+messageContent+"</span></span>";
                              }
                          }
                          
                          if(category.indexOf("回退")>-1){
                        	  category = "<span style='color:red;'>"+category+"</span>";
                          }else{
                        	  category = "<span style='color:blue;'>"+category+"</span>";
                          }
                          
                          
                          
                          
                          message+="<div style='line-height:18px'>"
                                     +"<img src='././images/msg/delete4.png' id='"+delNum1+"' onmouseout='changeDelPic1("+result[i].messageId+")' onmouseover='changeDelPic2("+result[i].messageId+")' style='height:9px;weidth:9px; cursor:pointer;display:block;float:left;margin-top:4px;' onclick='deleteMessage("+result[i].messageId+")'>"
                                     +"<img src='././images/msg/delete1.png' id='"+delNum2+"' onmouseout='changeDelPic1("+result[i].messageId+")' onmouseover='changeDelPic2("+result[i].messageId+")' style='height:9px;weidth:9px; cursor:pointer;display:none;float:left;margin-top:4px;' onclick='deleteMessage("+result[i].messageId+")'>"
                                     +"<a href='#' style='text-decoration:none;float:left;width:238px; overflow:hidden;height:15px; ' onclick=changeMessageState('"+result[i].messageLink+"',"+result[i].messageId+")>&nbsp;"+category+messageContent+"</a>"
                                   +"</div><br/>";
                      }
                      
                      $.messager.lays(250,200); 
                      $.messager.show("温馨提示",message,600,0);
                  }
               });
    }

   function  deleteMessage(messId){
          $.get("<%=request.getContextPath()%>/sysMess_deleteMessage.action?messId="+messId,
                  function(result){
                     $.messager.close();     
                  });
   }
   
   function changeMessageState(link,messId){
	   
       $.get("<%=request.getContextPath()%>/sysMess_changeMessageState.action?messId="+messId,
               function(result){
                  $.messager.close;     
               });
       
         window.parent.document.getElementById('mainframe').src= link;        
   }
   
   function getAllMessage(messId){
       $.get("<%=request.getContextPath()%>/sysMess_getAllMessage.action?messId="
						+ messId, function(result) {
					$.messager.close();
				});

	}

	/*
	window.onscroll = function(){
	    message.style.top=(document.body.clientHeight-parseInt(message.style.height)+document.body.scrollTop);   
	}  
	 */

	function changeDelPic1(id) {
		var delNum1 = "a_11_" + id;
		var delNum2 = "a_22_" + id;
		document.getElementById(delNum1).style.display = "block";
		document.getElementById(delNum2).style.display = "none";
	}

	function changeDelPic2(id) {
		var delNum1 = "a_11_" + id;
		var delNum2 = "a_22_" + id;
		document.getElementById(delNum1).style.display = "none";
		document.getElementById(delNum2).style.display = "block";

	}
	$(function() {
		$("a#fancy_dialog")
				.fancybox(
						{
							'width' : '75%',
							'height' : '80%',
							'autoScale' : false,
							'transitionIn' : 'fade',
							'transitionOut' : 'elastic',
							'type' : 'iframe',
							'hideOnOverlayClick' : false,
							'speedIn' : 300,
							'speedOut' : 300,
							'scrolling' : 'no',
							'enableEscapeButton' : true,
							'overlayOpacity' : 0.4,
							'overlayColor' : '#888', //#7FFFD4
							'opacity' : true,
							'onCleanup' : function() {
								if (typeof document
										.getElementById("fancybox-frame").contentWindow.closeDialog == "function") {
									var mainFrame = document
											.getElementById("mainframe");
									return document
											.getElementById("fancybox-frame").contentWindow
											.closeDialog(mainFrame);
								}
								return true;
							},
							'onClosed' : function() {
							}
						});
	});
	function openDialog(url) {
		$("a#fancy_dialog").attr("href", url).click();
	}
</script>
</head>

<body onload="ShowIframe('<%=request.getAttribute("updatePass")%>')">
	<a id="fancy_dialog"></a>
	<input type="hidden" id="funcCode" value="${funcCode}" />
	<input type="hidden" id="funcUrl" value="${funcUrl}" />
 	<div class="bigbox">
		<%
			request.setAttribute("funcCode", request.getParameter("funcCode"));
		%>
		
		<div id="alldivTreeArea" class="main_content">
			<div id="divTree" class="red_left" style="overflow-y:auto;display:none"></div>
			<div class="red_middle" style="display:none">
				<table border="0" cellpadding="0" cellspacing="0" height="100%">
					<tr>
						<td valign="middle"><a href="#" onclick="setmenu()"><img
								id="menuButton" type="0" src="images/left.gif" width="8"
								height="100%" border="0" /> </a></td>
					</tr>
				</table>
			</div>
			<div  style="padding-bottom:5px;	float: left;
	height: 100%;
	height:auto!important;
	width:100%;
	bottom: 0px;	 
	 position:absolute!important;
    position:relative;
	 top:0px!important;
	top:0px; 
	overflow:hidden;">
				<!--  <iframe id="mainframe" width="100%" height="800" scrolling="no" -->
				 <iframe scrolling="auto" class="yqy" id="mainframe"
					src="/contentMgt_personalSpaceIndex.action" frameborder="0"></iframe>
			</div>
		</div>
	</div>
	
	<!-- JS遮罩层 -->
	<div id="fullbg"></div>
	<!-- JS遮罩层结束 -->
	<!-- 遮罩层对话框 -->
	<div id="dialog">
		<div id="dialog_content">
			<table width="253" border="0" cellpadding="0" cellspacing="0"
				style="line-height:0px;">
				<tr>
					<td width="18" height="41"><img src="images/tab01.png"
						width="18" height="41" /></td>
					<td align="center"
						style="background:url(images/tab02.png) repeat-x 0;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td width="88%" align="left" valign="bottom"
									style="position:relative; top:-9px; left:6px"><span
									class="winclass" id="wintitle">新建窗口</span></td>
								<td width="12%" align="right">
									<table border="0" cellpadding="0" cellspacing="0"
										style="line-height:0px;">
										<tr>
											<td height="12">&nbsp;</td>
										</tr>
										<tr>
											<td height="20"><a href="#" onclick="closeBg();"
												title="关闭 "><img src="images/tab10.png" width="47"
													height="20" border="0" /> </a></td>
										</tr>
										<tr>
											<td height="9">&nbsp;</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
					<td align="right"><img src="images/tab03.png" width="18"
						height="41" /></td>
				</tr>
				<tr>
					<td height="35" style="background-image:url(images/tab04.png)">&nbsp;</td>
					<td width="216"><iframe id="sys_win" width="500" height="400"
							src="" frameborder="0" scrolling="auto"></iframe></td>
					<td width="18" style="background-image:url(images/tab05.png)">&nbsp;</td>
				</tr>
				<tr>
					<td width="18" height="18"><img src="images/tab06.png"
						width="18" height="18" /></td>
					<td style="background-image:url(images/tab07.png)">&nbsp;</td>
					<td width="18" height="18"><img src="images/tab08.png"
						width="18" height="18" /></td>
				</tr>
			</table>
		</div>
	</div>
	<%
		String showMessage = (String) request.getAttribute("showMessage");
		if ("1".equals(showMessage)) {
	%>
	<script type="text/javascript">
		showMessage();
		setInterval(showMessage, 60000);
	</script>
	<%
		}
	%>
	<script>
		//树形结构初始化-----------------------------------------------------////
		$(document)
				.ready(
						function() {
							var funcCode = $('#funcCode').val();							
							var funcUrl = $('#funcUrl').val();
							tree = new dhtmlXTreeObject("divTree", "100%",
									"100%", -1);
							//link tree to asp script
							tree.setImagePath("${ssourl}/background/codebase/imgs/csh_bluefolders/");
							tree.setSkin("dhx_skyblue");
							tree.setXMLAutoLoading("frameworkMgt_getRootMenuXML.action");
							//load first level of tree
							tree.setOnClickHandler(displayPic);//设置鼠标左键单击事件
							tree.setOnOpenHandler(displayPic);
							 tree.setOnRightClickHandler(openOtherUrl);
							tree.attachEvent("onClick", requestUrl);
							tree.enableHighlighting(true);
							tree.attachEvent("onXLE", function(tree, id) {
								removewait();
								var subTreeIds = tree.getAllSubItems(id);
								var idArr = subTreeIds.split(",");
								for ( var i = 0; i < idArr.length; i++) {
									var iscolor = tree.getUserData(idArr[i],
											"iscolor");
									if (iscolor == 1) {
										tree.setItemColor(idArr[i], "#aaa",
												"#aaa");
									}

								}
							});

							var indexUrl = '<s:url action="contentMgt_personalSpaceIndex" namespace="/" />';
							openIndex(indexUrl);

							$("#link").attr("title", "keleyi.com");
							tree.setOnMouseOutHandler(ouseOutHandlerFunc);
							if (funcCode!= "" && funcUrl != "" && funcUrl != "undefined"){
								openSelf(funcUrl, funcCode);
							}else{
								tree._loadDynXML("frameworkMgt_getRootMenuXML.action?id="+ funcCode);
							}
						});

		function ouseOutHandlerFunc(id) {
			$(".standartTreeRow").each(function() {
				$(this).attr("title", $(this).text());
			});

		}

		function displayPic(id) {
			var state = tree.getOpenState(id);//获得当前点击合拢状态
			if (state <= 0) {
				//beginwait("alldivTreeArea",'正在加载菜单');
				tree.deleteChildItems(id);
				tree.setXMLAutoLoading("frameworkMgt_getSubMenuXML.action");
				tree._loadDynXML("frameworkMgt_getSubMenuXML.action?id=" + id);
			} else {
				tree.closeAllItems(id);
			}
		}

		function requestUrl(id) {
			if (tree.getUserData(id, "url") != null) {
				var myDate = new Date();
				var mytime = myDate.getTime();
				var url = tree.getUserData(id, "url");
				url += "&tempStr=" + mytime;
				$(window.parent.document.getElementById("mainframe").src = url);
			}
			return true;
		}
		function openOtherUrl(id) {
			if (tree.getUserData(id, "url") != null) {
				var myDate = new Date();
				var mytime = myDate.getTime();
				var url = tree.getUserData(id, "url");
				url += "&tempStr=" + mytime;
				window.open(url);
			}
		}
		function openSelf(url, funcCode) {
			//beginwait("alldivTreeArea",'正在加载菜单');
			tree.deleteChildItems("-1");
			tree.setXMLAutoLoading("frameworkMgt_getRootMenuXML.action");
			tree._loadDynXML("frameworkMgt_getRootMenuXML.action?id="+ funcCode);
				//tree.openAllItems("-1");
				var subTreeIds = tree.getAllSubItems("-1");
				var idArr = subTreeIds.split(",");
				for ( var i = 0; i < idArr.length; i++) {
					tree.openAllItems(idArr[i]);
				}
			$("#divTree > .containerTableStyle").show();
			$("#divTree > #indexMenu").remove();
			$(window.parent.document.getElementById("mainframe").src = url);
			// $(window.parent.document.getElementById("mainframe")).reload();
			//document.getElementById("mainFrame").src=url;
		}

		function openIndex(indexUrl) {
			var menuUrl = '<s:url action="contentMgt_spaceIndexMenu" namespace="/" />';
			var wid = $("#divTree").width();
			//           	"<iframe src='"+menuUrl+"' width='"+wid+"' height='100%' scrolling='no' frameborder='0' />"
			$("#divTree > .containerTableStyle").hide();
			$("#divTree > #indexMenu").remove();
			$("#divTree").append("<iframe id='indexMenu' src='"
									+ menuUrl
									+ "' width='"
									+ wid
									+ "' height='100%' scrolling='auto' frameborder='0' />");
			window.parent.document.getElementById("mainframe").src = indexUrl;
		}

		function openSpaceIndex(indexUrl, idIndex) {

			for ( var i = 1; i <= 4; i++) {
				if (i == idIndex) {
					$("#spaceIndex_" + i).css({
						color : "#E90000"
					});
				} else {
					$("#spaceIndex_" + i).css({
						color : "#2F2F2F"
					});
				}
			}
			var menuUrl = '<s:url action="contentMgt_spaceIndexMenu" namespace="/" />';
			var wid = $("#divTree").width();

			$("#divTree > .containerTableStyle").hide();
			$("#divTree > #indexMenu").remove();
			$("#divTree").append("<iframe id='indexMenu' src='"
									+ menuUrl
									+ "' width='"
									+ wid
									+ "' height='100%' scrolling='no' frameborder='0' />");
			window.parent.document.getElementById("mainframe").src = indexUrl;
		}

		function showMoreMessage() {
			var url = "/officeMgt_myMessage.action";
			window.parent.document.getElementById("mainframe").src = url;
		}
	</script>

	<!-- 遮罩层对话框结束 -->
	<%
		String count = request.getParameter("count");
		if ("0".equals(count)) {
	%>
	<script>
		setMainFrame('taskMgt_principalTaskList.action?funcCode=27-9-3-');
	</script>
	<%
		}
		if ("1".equals(count)) {
	%>
	<script>
		setMainFrame('/workflowEngine/background/templeMgtAction_processingWFList.action?bizType=-1&funcCode=27-1-2-');
	</script>
	<%
		}
	%>
	<br />
	<br />
	<center>
		<!-- <div class="nav_line" style="width: 1000px;"></div><br/><br/>
    	<table border="0" width="1000px" style="font-family: 宋体;font-size: 13px;color: #999999">
    			<tr>
    				<td width="70%">&nbsp;</td>
    				<td width="15%" align="right" ><img src="images/person.jpg" style="position: absolute;margin-top:-10px;margin-left:-30px; " alt="联系我们" title="联系我们"/><a href="#" onclick="javascript:window.parent.document.getElementById('mainframe').src='/userMgt_contactus.action'">联系我们</a></td>
    				<td align="left">&nbsp;&nbsp;&nbsp;<img src="images/pen.png" height="26" width="26" style="position: absolute;margin-top:-8px;margin-left:-10px; " title="问题反馈"/>&nbsp;&nbsp;&nbsp;<a href="#" onclick="javascript:window.parent.document.getElementById('mainframe').src='/workflowEngine/background/templeMgtAction_myWFList.action?bizType=1118'">问题反馈</a></td>
    			</tr>
    		</table> -->
	</center>
</body>
</html>

