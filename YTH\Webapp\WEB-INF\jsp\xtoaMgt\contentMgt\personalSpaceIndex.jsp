<%@page import="com.ccidit.platform.sdk.UserVO"%>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<base href="<%=basePath%>">

<title></title>

<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
<!--
	<link rel="stylesheet" type="text/css" href="styles.css">
	-->
<link rel="stylesheet" href="${pageContext.request.contextPath }/js/jquery/UI/UI_CSS/ui.all.css">
<script src="${pageContext.request.contextPath }/js/jquery-1.7.2.min.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/ui.core.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.widget.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.mouse.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.sortable.js"></script>
<script src="${pageContext.request.contextPath }/js/platformInfo.js"></script>

<style type="text/css">
body {
    margin: 0;
    padding: 10px;
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: #f5f5f5;
}

.workspace-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.module-box {
    background: white;
    border: 1px solid #ddd;
    border-radius: 0;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.module-header {
    background: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    margin: 0;
    border-bottom: 2px solid #87ceeb;
    position: relative;
}

.module-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #4a90e2, #87ceeb);
}

.module-title {
    font-size: 14px;
    font-weight: bold;
    color: #4a90e2;
    margin-left: 10px;
}

.module-content {
    padding: 15px;
    background: white;
}

.more-link {
    color: white;
    text-decoration: none;
    font-size: 12px;
}

.more-link:hover {
    color: #e0e0e0;
}

.item-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.item-list li {
    padding: 8px 0;
    border-bottom: 1px dotted #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-list li:last-child {
    border-bottom: none;
}

.item-title {
    color: #333;
    text-decoration: none;
    flex: 1;
    margin-right: 10px;
}

.item-title:hover {
    color: #0066cc;
}

.item-date {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
}

.item-meta {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.schedule-item {
    margin-bottom: 15px;
    padding: 0;
    background: white;
    border-radius: 0;
}

.schedule-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.no-data {
    text-align: center;
    color: #999;
    padding: 20px;
    font-style: italic;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // 初始化页面数据
    loadMySchedule();
    loadWorkDynamics();
    loadProcessFlow();
    loadNotifications();
    loadMyTodos();
    loadPublicNotices();
});

function loadMySchedule() {
    // 模拟加载我的日程数据
    // 实际项目中这里应该是AJAX调用
}

function loadWorkDynamics() {
    // 模拟加载局内工作动态
}

function loadProcessFlow() {
    // 模拟加载过程程流转
}

function loadNotifications() {
    // 模拟加载通知公告
}

function loadMyTodos() {
    // 模拟加载我的待办
}

function loadPublicNotices() {
    // 模拟加载公示栏
}
</script>
</head>

<body>
<div class="workspace-container">
    <!-- 我的日程 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">我的日程</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <div class="schedule-item">
                <div class="schedule-title">会见中建国际副总经理协议签一行</div>
                <div class="item-meta">地点：大会议室　时间：2025-7-10 14:30 - 15:30</div>
                <div class="item-meta">发布部门：对外投资合作司（海外会展联合会议办公室）</div>
            </div>
            <div class="schedule-item">
                <div class="schedule-title">与澳洲云肯省董事长一行会谈（视频会议）</div>
                <div class="item-meta">地点：大会议室　时间：2025-7-10 16:00 - 17:00</div>
                <div class="item-meta">发布部门：对外投资合作司（海外会展联合会议办公室）</div>
            </div>
        </div>
    </div>

    <!-- 局内工作动态 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">局内工作动态</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">关于与中经宏观（北京）文化传媒有限公司签署合作协议的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于与国际大学创新联盟、广州澳众智能科技有限公司签订合作协议的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
            </ul>
        </div>
    </div>

    <!-- 过程程流转 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">过程程流转</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <div class="no-data">暂无数据</div>
        </div>
    </div>

    <!-- 通知公告 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">通知公告</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">关于举办商务部干部职工羽毛球赛的通知</a>
                    <span class="item-date">2025-07-08</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于协和体检的补充通知</a>
                    <span class="item-date">2025-07-07</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于在工作平台中新增过程流转功能的通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">临时断网通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于发布2025年绩效考核任务的通知</a>
                    <span class="item-date">2025-07-03</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- 我的待办 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">我的待办</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">2025.7.8项目信息、研究报告上网审核</a>
                </li>
                <li>
                    <a href="#" class="item-title">20250704中国投资指南网引进来资讯栏目上网信息审核</a>
                </li>
                <li>
                    <a href="#" class="item-title">休年假　车休假</a>
                </li>
                <li>
                    <a href="#" class="item-title">2025.7.3境外投资统计、项目信息上网审核</a>
                </li>
            </ul>
        </div>
    </div>

    <!-- 公示栏 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">公示栏</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">关于举办商务部干部职工羽毛球赛的通知</a>
                    <span class="item-date">2025-07-08</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于协和体检的补充通知</a>
                    <span class="item-date">2025-07-07</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于在工作平台中新增过程流转功能的通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">临时断网通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于发布2025年绩效考核任务的通知</a>
                    <span class="item-date">2025-07-03</span>
                </li>
            </ul>
        </div>
    </div>
</div>
</body>
</html>
